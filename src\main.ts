import { createApp } from "vue";
import App from "./App.vue";
import setupPlugins from "@/plugins";
// import ganttastic from '@infectoone/vue-ganttastic'
// 注释掉旧的甘特图组件，准备使用 vue-gantt-3
// import VueGantt3 from 'vue-gantt-3';
// import 'vue-gantt-3/es/vue-gantt-3.css';

// 本地SVG图标
import "virtual:svg-icons-register";

// 样式
import "element-plus/theme-chalk/dark/css-vars.css";
import "@/styles/index.scss";
import "uno.css";
import "animate.css";
import { InstallCodeMirror } from "codemirror-editor-vue3";

const app = createApp(App);
// 注册插件
app.use(setupPlugins);
app.use(InstallCodeMirror);
// app.use(ganttastic)
// 注释掉旧的甘特图组件
// app.use(VueGantt3);

app.mount("#app");
